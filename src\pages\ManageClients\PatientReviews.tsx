import { useUIStore } from "@/stores/uiStore";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Star, Download, Info, ChevronRight } from "lucide-react";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { FeedbackCard } from "@/components/ui-components/FeedBackCard";
import { StarCard } from "@/components/ui-components/StarCard";
import type { MetricData } from "@/components/ui-components/FeedBackCard";
import type { StarRating } from "@/components/ui-components/StarCard";
import { InputText } from "@/components/common/InputText";
import { Checkbox } from "@/components/common/Checkbox";
import { Search, Settings2 } from "lucide-react";
import { PatientFeedbackSheet } from "@/components/dashboard/patient/PatientFeedbackSheet";
import { SurveyQuestionSheet } from "@/components/dashboard/patient/SurveyQuestionSheet";
import { ScaleSurveyQuestionSheet } from "@/components/dashboard/patient/ScaleSurveyQuestionSheet";
import { YesNoSurveyQuestionSheet } from "@/components/dashboard/patient/YesNoSurveyQuestionSheet";
import { LikertScaleSurveyQuestionSheet } from "@/components/dashboard/patient/LikertScaleSurveyQuestionSheet";
import { TextResponseSurveyQuestionSheet } from "@/components/dashboard/patient/TextResponseSurveyQuestionSheet";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	patientExperienceApi,
	type PatientFeedback,
	type PatientExperienceStats,
} from "@/lib/api/patientExperienceApi";

interface Review {
	id: string;
	patientName: string;
	patientInitials: string;
	rating: number;
	comment: string;
	date: string;
	service: string;
	status: "published" | "pending" | "hidden";
	stationName: string;
	locationName: string;
	patientAvatar: string;
}

interface SummaryQuestion {
	id: string;
	question: string;
	fullQuestion?: string;
	station: string;
	location: string;
	service: string;
	responses: number;
	type: string;
}

export default function PatientReviews() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);
	const { organizationId } = useOrganizationContext();

	const [feedbacks, setFeedbacks] = useState<PatientFeedback[]>([]);
	const [isLoadingFeedbacks, setIsLoadingFeedbacks] = useState(false);
	const [feedbackError, setFeedbackError] = useState<string | null>(null);

	// Helper function to get patient initials from name
	const getPatientInitials = (name: string): string => {
		return name
			.split(" ")
			.map((word) => word.charAt(0).toUpperCase())
			.join("")
			.slice(0, 2);
	};

	// Helper function to format date
	const formatDate = (dateString: string): string => {
		try {
			const date = new Date(dateString);
			return date.toLocaleDateString("en-US", {
				day: "2-digit",
				month: "short",
				year: "numeric",
				hour: "2-digit",
				minute: "2-digit",
				hour12: true,
			});
		} catch {
			return dateString;
		}
	};

	const [filter] = useState<"all" | "published" | "pending" | "hidden">(
		"all"
	);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
	const [activeTab, setActiveTab] = useState<"feedback" | "summary">(
		"feedback"
	);
	const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
	const [feedbackSheetOpen, setFeedbackSheetOpen] = useState(false);
	const [selectedReview, setSelectedReview] = useState<Review | null>(null);
	const [surveyQuestionSheetOpen, setSurveyQuestionSheetOpen] =
		useState(false);
	const [scaleSurveyQuestionSheetOpen, setScaleSurveyQuestionSheetOpen] =
		useState(false);
	const [yesNoSurveyQuestionSheetOpen, setYesNoSurveyQuestionSheetOpen] =
		useState(false);
	const [
		likertScaleSurveyQuestionSheetOpen,
		setLikertScaleSurveyQuestionSheetOpen,
	] = useState(false);
	const [
		textResponseSurveyQuestionSheetOpen,
		setTextResponseSurveyQuestionSheetOpen,
	] = useState(false);
	const [selectedQuestion, setSelectedQuestion] =
		useState<SummaryQuestion | null>(null);
	const [statsData, setStatsData] = useState<PatientExperienceStats | null>(
		null
	);
	const [isLoadingStats, setIsLoadingStats] = useState(false);

	const [summaryQuestions] = useState<SummaryQuestion[]>([
		{
			id: "1",
			question: "Quality of Service",
			fullQuestion:
				"How would you rate the overall quality of service you received?",
			station: "Dr. Sullivan Abraham",
			location: "Medlabs, Ontario",
			service: "Consultancy",
			responses: 12,
			type: "Text",
		},
		{
			id: "2",
			question: "Performance Excellence",
			fullQuestion:
				"In what areas do you believe you have excelled and demonstrated exceptional performance?",
			station: "Dr. Sullivan Abraham",
			location: "Medlabs, Ontario",
			service: "Consultancy",
			responses: 925,
			type: "Open Text",
		},
		{
			id: "3",
			question: "Performance Excellence",
			fullQuestion:
				"In what areas do you believe you have excelled and demonstrated exceptional performance?",
			station: "Dr. Sullivan Abraham",
			location: "Medlabs, Ontario",
			service: "Consultancy",
			responses: 925,
			type: "Scale (Disagree to Agree)",
		},
		{
			id: "4",
			question: "COVID Symptoms",
			fullQuestion:
				"Have you shown any symptoms related to Covid in the past 2 weeks?",
			station: "Dr. Sullivan Abraham",
			location: "Medlabs, Ontario",
			service: "Consultancy",
			responses: 925,
			type: "Yes / No",
		},
		{
			id: "5",
			question: "Pain Level",
			fullQuestion:
				"On a scale of 1 - 10, 1 being the lowest, how much pain are you in right now?",
			station: "Dr. Sullivan Abraham",
			location: "Medlabs, Ontario",
			service: "Consultancy",
			responses: 925,
			type: "1 - 10 Scale",
		},
		{
			id: "6",
			question: "Would Recommend",
			station: "Dr. Sullivan Abraham",
			location: "Medlabs, Ontario",
			service: "Consultancy",
			responses: 12,
			type: "Custom Checkbox",
		},
		{
			id: "7",
			question: "Which symptoms are you feeling?",
			station: "Dr. Sullivan Abraham",
			location: "Medlabs, Ontario",
			service: "Consultancy",
			responses: 925,
			type: "Custom Checkbox",
		},
	]);

	useEffect(() => {
		const fetchData = async () => {
			if (!organizationId) return;

			setIsLoadingStats(true);
			setIsLoadingFeedbacks(true);
			setFeedbackError(null);

			try {
				// Fetch stats and feedbacks in parallel
				const [statsResponse, feedbacksResponse] = await Promise.all([
					patientExperienceApi.getPatientExperienceStats(
						organizationId
					),
					patientExperienceApi.getPatientExperience({
						organization_id: organizationId,
						per_page: 100, // Increased to get more feedback entries
					}),
				]);

				setStatsData(statsResponse.data.stats);
				setFeedbacks(feedbacksResponse.data.feedbacks);
			} catch (error) {
				console.error(
					"Failed to fetch patient experience data:",
					error
				);
				setFeedbackError(
					"Failed to load feedback data. Please try again."
				);
			} finally {
				setIsLoadingStats(false);
				setIsLoadingFeedbacks(false);
			}
		};

		fetchData();
	}, [organizationId]);

	useEffect(() => {
		setBreadcrumbs([
			{ label: "Dashboard", href: "/" },
			{ label: "Patients", href: "/dashboard/patients" },
			{ label: "Feedback", href: "/dashboard/patients/reviews" },
		]);

		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<div>
					<h1 className="text-foreground text-2xl font-bold">
						Patient Reviews
					</h1>
				</div>
				<div className="flex items-center gap-2">
					<Button variant="outline" size="sm">
						<Download className="mr-2 h-4 w-4" />
						Export
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [setPageHeaderContent]);

	// Convert feedbacks to review format for compatibility
	const reviews = feedbacks.map((feedback) => ({
		id: feedback.id,
		patientName: feedback.patient_name,
		patientInitials: getPatientInitials(feedback.patient_name),
		rating: feedback.rating,
		comment: "", // API doesn't provide comment in list view
		date: formatDate(feedback.date),
		service: feedback.service_name,
		status: "published" as const,
		stationName: feedback.station_name,
		locationName: feedback.location_name,
		patientAvatar: feedback.patient_avatar,
	}));

	const filteredReviews = reviews.filter(
		(review) => filter === "all" || review.status === filter
	);

	const searchedReviews = filteredReviews.filter(
		(review) =>
			review.patientName
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			review.service.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const searchedQuestions = summaryQuestions.filter(
		(question) =>
			question.question
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			question.service.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const averageRating = statsData?.average_rating || 0;

	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			setSelectedReviews(searchedReviews.map((review) => review.id));
		} else {
			setSelectedReviews([]);
		}
	};

	const handleReviewSelection = (reviewId: string, checked: boolean) => {
		if (checked) {
			setSelectedReviews((prev) => [...prev, reviewId]);
		} else {
			setSelectedReviews((prev) => prev.filter((id) => id !== reviewId));
		}
	};

	const handleSelectAllQuestions = (checked: boolean) => {
		if (checked) {
			setSelectedQuestions(
				searchedQuestions.map((question) => question.id)
			);
		} else {
			setSelectedQuestions([]);
		}
	};

	const handleQuestionSelection = (questionId: string, checked: boolean) => {
		if (checked) {
			setSelectedQuestions((prev) => [...prev, questionId]);
		} else {
			setSelectedQuestions((prev) =>
				prev.filter((id) => id !== questionId)
			);
		}
	};

	const handleViewFeedback = (review: Review) => {
		setSelectedReview(review);
		setFeedbackSheetOpen(true);
	};

	const getResponseDataForQuestion = (question: SummaryQuestion) => {
		switch (question.type) {
			case "Custom Checkbox":
				if (question.question === "Which symptoms are you feeling?") {
					return [
						{
							id: "1",
							label: "Cough",
							count: 200,
							percentage: 72,
						},
						{
							id: "2",
							label: "Flu",
							count: 240,
							percentage: 14,
						},
						{
							id: "3",
							label: "Itchy",
							count: 200,
							percentage: 60,
						},
						{
							id: "4",
							label: "Nauseas",
							count: 80,
							percentage: 14,
						},
						{
							id: "5",
							label: "Seasick",
							count: 32,
							percentage: 5,
						},
					];
				} else {
					return [
						{
							id: "1",
							label: "Option A",
							count: 8,
							percentage: 67,
						},
						{
							id: "2",
							label: "Option B",
							count: 3,
							percentage: 25,
						},
						{
							id: "3",
							label: "Option C",
							count: 1,
							percentage: 8,
						},
					];
				}
			case "Scale (Disagree to Agree)":
				return [
					{
						id: "1",
						label: "Strongly Agree",
						count: 275,
						percentage: 29.7,
						color: "bg-teal-600",
					},
					{
						id: "2",
						label: "Agree",
						count: 90,
						percentage: 9.7,
						color: "bg-blue-600",
					},
					{
						id: "3",
						label: "Neutral",
						count: 173,
						percentage: 18.7,
						color: "bg-slate-700",
					},
					{
						id: "4",
						label: "Disagree",
						count: 187,
						percentage: 20.2,
						color: "bg-orange-300",
					},
					{
						id: "5",
						label: "Strongly Disagree",
						count: 200,
						percentage: 21.6,
						color: "bg-red-400",
					},
				];
			case "Yes / No":
				return [
					{
						id: "1",
						label: "Yes",
						count: 275,
						percentage: 72.5,
						color: "bg-teal-600",
					},
					{
						id: "2",
						label: "No",
						count: 104,
						percentage: 27.5,
						color: "bg-cyan-700",
					},
				];
			case "1 - 10 Scale":
				return [
					{
						id: "1",
						label: "1",
						count: 275,
						percentage: 30,
						color: "bg-teal-600",
					},
					{
						id: "2",
						label: "2",
						count: 90,
						percentage: 10,
						color: "bg-cyan-700",
					},
					{
						id: "3",
						label: "3",
						count: 90,
						percentage: 10,
						color: "bg-sky-400",
					},
					{
						id: "4",
						label: "4",
						count: 173,
						percentage: 19,
						color: "bg-blue-300",
					},
					{
						id: "5",
						label: "5",
						count: 100,
						percentage: 11,
						color: "bg-slate-700",
					},
					{
						id: "6",
						label: "6",
						count: 80,
						percentage: 9,
						color: "bg-purple-400",
					},
					{
						id: "7",
						label: "7",
						count: 187,
						percentage: 20,
						color: "bg-orange-300",
					},
					{
						id: "8",
						label: "8",
						count: 96,
						percentage: 10,
						color: "bg-yellow-600",
					},
					{
						id: "9",
						label: "9",
						count: 104,
						percentage: 11,
						color: "bg-red-400",
					},
					{
						id: "10",
						label: "10",
						count: 98,
						percentage: 11,
						color: "bg-emerald-300",
					},
				];
			default:
				return [
					{
						id: "1",
						label: "Response 1",
						count: 8,
						percentage: 67,
					},
					{
						id: "2",
						label: "Response 2",
						count: 3,
						percentage: 25,
					},
					{
						id: "3",
						label: "Response 3",
						count: 1,
						percentage: 8,
					},
				];
		}
	};

	const handleViewSurveyQuestion = (question: SummaryQuestion) => {
		setSelectedQuestion(question);
		if (question.type === "1 - 10 Scale") {
			setScaleSurveyQuestionSheetOpen(true);
		} else if (question.type === "Yes / No") {
			setYesNoSurveyQuestionSheetOpen(true);
		} else if (question.type === "Scale (Disagree to Agree)") {
			setLikertScaleSurveyQuestionSheetOpen(true);
		} else if (question.type === "Open Text" || question.type === "Text") {
			setTextResponseSurveyQuestionSheetOpen(true);
		} else {
			setSurveyQuestionSheetOpen(true);
		}
	};

	const renderStars = (rating: number) => {
		return Array.from({ length: 5 }, (_, index) => (
			<Star
				key={index}
				className={`h-3.5 w-3.5 ${
					index < rating
						? "fill-amber-500 text-amber-500"
						: "fill-gray-300 text-gray-300"
				}`}
			/>
		));
	};

	if (isLoadingFeedbacks) {
		return (
			<div className="flex h-full flex-col items-center justify-center">
				<div className="text-gray-500">Loading feedback data...</div>
			</div>
		);
	}

	if (feedbackError) {
		return (
			<div className="flex h-full flex-col items-center justify-center">
				<div className="mb-4 text-red-500">{feedbackError}</div>
				<Button
					onClick={() => window.location.reload()}
					variant="outline"
				>
					Retry
				</Button>
			</div>
		);
	}

	if (feedbacks.length === 0) {
		return (
			<div className="flex h-full flex-col">
				<EmptyContent
					title="No Reviews Found"
					description="Patient reviews will appear here once they start providing feedback."
					actions={[
						{
							label: "View Settings",
							onClick: () => console.log("View settings"),
							variant: "primary",
						},
					]}
				/>

				{/* Survey Question Sheet */}
				<SurveyQuestionSheet
					open={surveyQuestionSheetOpen}
					onOpenChange={setSurveyQuestionSheetOpen}
					questionData={
						selectedQuestion
							? {
									question:
										selectedQuestion.fullQuestion ||
										selectedQuestion.question,
									type: selectedQuestion.type,
									totalResponses: selectedQuestion.responses,
									responses:
										getResponseDataForQuestion(
											selectedQuestion
										),
								}
							: undefined
					}
				/>
			</div>
		);
	}

	const submissionMetrics: MetricData[] = [
		{
			value: statsData?.total_submissions?.toString() || "0",
			label: "Submission",
			percentage: statsData?.completion_rate || 0,
			primaryColor: "#4F46E5",
			secondaryColor: "#CBD5E1",
			primaryLabel: "Completed",
			secondaryLabel: "Incomplete",
		},
		{
			value: `${statsData?.completion_rate || 0}%`,
			label: "Completion Rate",
			percentage: statsData?.completion_rate || 0,
			primaryColor: "#EF4444",
			secondaryColor: "#FCA5A5",
			primaryLabel: "Submitted",
			secondaryLabel: "Drop-Off",
		},
	];

	// Calculate star ratings from API data
	const totalRatings = statsData
		? Object.values(statsData.rating_distribution).reduce(
				(sum, count) => sum + count,
				0
			)
		: 0;
	const starRatings: StarRating[] = [
		{
			stars: 1,
			count: statsData?.rating_distribution["1"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.rating_distribution["1"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 2,
			count: statsData?.rating_distribution["2"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.rating_distribution["2"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 3,
			count: statsData?.rating_distribution["3"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.rating_distribution["3"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 4,
			count: statsData?.rating_distribution["4"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.rating_distribution["4"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 5,
			count: statsData?.rating_distribution["5"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.rating_distribution["5"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
	];

	return (
		<div className="flex h-full flex-col space-y-6 pt-2">
			<div className="inline-flex items-start justify-start gap-3 self-stretch">
				{isLoadingStats ? (
					<div className="flex h-32 w-full items-center justify-center">
						<div className="text-gray-500">
							Loading statistics...
						</div>
					</div>
				) : (
					<>
						<FeedbackCard
							rating={averageRating}
							reviewCount={statsData?.total_reviews || 0}
							metrics={submissionMetrics}
							className="flex-shrink-0"
						/>
						<StarCard
							ratings={starRatings}
							barColor="bg-amber-500"
							className=""
						/>
					</>
				)}
			</div>

			<div className="inline-flex flex-col items-start justify-start gap-1 self-stretch rounded-2xl">
				<div className="inline-flex items-center justify-between self-stretch">
					<div className="flex h-10 items-center justify-start rounded-lg bg-[#F4F4F5] p-1">
						<div
							className={`flex h-8 cursor-pointer items-center justify-center gap-2 rounded-md px-3 py-1 ${
								activeTab === "feedback"
									? "bg-white shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]"
									: ""
							}`}
							onClick={() => setActiveTab("feedback")}
						>
							<div
								className={`justify-start text-center text-xs leading-none ${
									activeTab === "feedback"
										? "font-semibold text-gray-900"
										: "font-medium text-gray-500"
								}`}
							>
								Feedback
							</div>
						</div>
						<div
							className={`flex h-8 cursor-pointer items-center justify-center gap-2 rounded-md px-3 py-1 ${
								activeTab === "summary"
									? "bg-white shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]"
									: ""
							}`}
							onClick={() => setActiveTab("summary")}
						>
							<div
								className={`justify-start text-center text-xs leading-none ${
									activeTab === "summary"
										? "font-semibold text-gray-900"
										: "font-medium text-gray-500"
								}`}
							>
								Summary
							</div>
						</div>
					</div>
					<div className="flex w-[636px] items-center justify-end gap-2.5">
						<Button
							variant="outline"
							size="icon"
							className="h-9 w-9"
						>
							<Settings2 className="h-4 w-4" />
						</Button>
						<InputText
							variant="with-icon"
							icon={<Search className="h-3 w-3" />}
							iconPosition="left"
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="h-9 w-56"
						/>
					</div>
				</div>

				<div className="flex flex-col items-start justify-start gap-1 self-stretch overflow-hidden rounded-xl border border-[#E4E4E7]">
					{activeTab === "feedback" ? (
						<>
							<div className="inline-flex items-center justify-start self-stretch">
								<div className="flex h-12 items-center justify-start gap-2.5 px-4">
									<Checkbox
										checked={
											searchedReviews.length > 0 &&
											searchedReviews.every((review) =>
												selectedReviews.includes(
													review.id
												)
											)
										}
										onCheckedChange={handleSelectAll}
									/>
								</div>
								<div className="flex h-12 min-w-20 flex-1 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Client Name
									</div>
								</div>
								<div className="flex h-12 w-40 items-center justify-start gap-2.5 px-3">
									<div className="justify-center text-xs leading-none font-normal text-gray-500">
										Station
									</div>
								</div>
								<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Location
									</div>
								</div>
								<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Service
									</div>
								</div>
								<div className="flex h-12 w-36 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Rating
									</div>
								</div>
								<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Date Added
									</div>
								</div>
								<div className="flex h-12 w-16 min-w-16 items-center justify-end gap-2.5 px-3" />
							</div>
						</>
					) : (
						<>
							{" "}
							<div className="inline-flex items-center justify-start self-stretch">
								<div className="flex h-12 items-center justify-start gap-2.5 px-4">
									<Checkbox
										checked={
											searchedQuestions.length > 0 &&
											searchedQuestions.every(
												(question) =>
													selectedQuestions.includes(
														question.id
													)
											)
										}
										onCheckedChange={
											handleSelectAllQuestions
										}
									/>
								</div>
								<div className="flex h-12 min-w-20 flex-1 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Question
									</div>
								</div>
								<div className="flex h-12 w-40 items-center justify-start gap-2.5 px-3">
									<div className="justify-center text-xs leading-none font-normal text-gray-500">
										Station
									</div>
								</div>
								<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Location
									</div>
								</div>
								<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Service
									</div>
								</div>
								<div className="flex h-12 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										No.of Reponses
									</div>
								</div>
								<div className="flex h-12 w-36 min-w-20 items-center justify-start gap-2.5 px-3">
									<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
										Type
									</div>
								</div>
								<div className="flex h-12 w-16 min-w-16 items-center justify-end gap-2.5 px-3" />
							</div>
						</>
					)}

					{/* Table Rows */}
					{activeTab === "feedback"
						? searchedReviews.map((review) => (
								<div
									key={review.id}
									className="inline-flex h-16 cursor-pointer items-center justify-start self-stretch border-t border-[#E4E4E7] bg-white hover:bg-gray-50"
									onClick={() => handleViewFeedback(review)}
								>
									<div
										className="flex items-center justify-start gap-2.5 self-stretch px-4"
										onClick={(e) => e.stopPropagation()}
									>
										<Checkbox
											checked={selectedReviews.includes(
												review.id
											)}
											onCheckedChange={(checked) =>
												handleReviewSelection(
													review.id,
													checked
												)
											}
										/>
									</div>
									<div className="flex min-w-20 flex-1 items-center justify-start gap-2 self-stretch px-3">
										<Avatar className="h-9 w-9">
											{review.patientAvatar ? (
												<img
													src={review.patientAvatar}
													alt={review.patientName}
													className="h-full w-full object-cover"
												/>
											) : (
												<AvatarFallback className="bg-gray-100">
													{review.patientInitials}
												</AvatarFallback>
											)}
										</Avatar>
										<div className="justify-center text-sm leading-tight font-medium text-gray-900">
											{review.patientName}
										</div>
									</div>
									<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{review.stationName}
											</div>
										</div>
									</div>
									<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{review.locationName}
											</div>
										</div>
									</div>
									<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{review.service}
											</div>
										</div>
									</div>
									<div className="flex w-36 min-w-20 items-center justify-start gap-2 self-stretch px-3">
										<div className="flex items-center justify-start gap-1.5">
											{renderStars(review.rating)}
										</div>
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{review.rating}
										</div>
									</div>
									<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{review.date}
											</div>
										</div>
									</div>
									<div
										className="flex min-w-16 items-center justify-end gap-1.5 self-stretch px-3"
										onClick={(e) => e.stopPropagation()}
									>
										<Button
											variant="outline"
											size="icon"
											className="h-6 w-6 p-2"
											onClick={() =>
												handleViewFeedback(review)
											}
										>
											<Info className="h-3 w-3" />
										</Button>
									</div>
								</div>
							))
						: searchedQuestions.map((question) => (
								<div
									key={question.id}
									className="inline-flex h-16 cursor-pointer items-center justify-start self-stretch border-t border-[#E4E4E7] bg-white hover:bg-gray-50"
									onClick={() =>
										handleViewSurveyQuestion(question)
									}
								>
									<div
										className="flex items-center justify-start gap-2.5 self-stretch px-4"
										onClick={(e) => e.stopPropagation()}
									>
										<Checkbox
											checked={selectedQuestions.includes(
												question.id
											)}
											onCheckedChange={(checked) =>
												handleQuestionSelection(
													question.id,
													checked
												)
											}
										/>
									</div>
									<div className="flex min-w-20 flex-1 items-center justify-start gap-3 self-stretch px-3">
										<div className="justify-center text-sm leading-tight font-medium text-gray-900">
											{question.question}
										</div>
									</div>
									<div className="flex h-16 w-40 min-w-20 items-center justify-start gap-3 px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{question.station}
											</div>
										</div>
									</div>
									<div className="flex h-16 w-40 min-w-20 items-center justify-start gap-3 px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{question.location}
											</div>
										</div>
									</div>
									<div className="flex h-16 w-40 min-w-20 items-center justify-start gap-3 px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{question.service}
											</div>
										</div>
									</div>
									<div className="flex w-28 min-w-20 items-center justify-start gap-3 self-stretch px-3">
										<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
											<div className="justify-start text-[10px] leading-3 font-medium text-gray-900">
												{question.responses}
											</div>
										</div>
									</div>
									<div className="flex w-36 min-w-20 items-center justify-start gap-3 self-stretch px-3">
										<div className="inline-flex flex-col items-start justify-start gap-0.5">
											<div className="justify-center text-xs leading-none font-normal text-gray-500">
												{question.type}
											</div>
										</div>
									</div>
									<div
										className="flex min-w-16 items-center justify-end gap-1.5 self-stretch px-3"
										onClick={(e) => e.stopPropagation()}
									>
										<Button
											variant="outline"
											size="icon"
											className="h-6 w-6 p-2"
											onClick={() =>
												handleViewSurveyQuestion(
													question
												)
											}
										>
											<ChevronRight className="h-3 w-3" />
										</Button>
									</div>
								</div>
							))}
				</div>
			</div>

			{/* Patient Feedback Sheet */}
			<PatientFeedbackSheet
				open={feedbackSheetOpen}
				onOpenChange={setFeedbackSheetOpen}
				feedbackData={
					selectedReview
						? {
								patientName: selectedReview.patientName,
								patientInitials: selectedReview.patientInitials,
								date: selectedReview.date,
								rating: selectedReview.rating,
								service: selectedReview.service,
								doctor: "Dr. Steven Brown",
								location: "Dermatology Services",
								questions: [
									{
										id: "1",
										question:
											"I found booking my appointment online easy.",
										answer: "Strongly Agree",
									},
									{
										id: "2",
										question:
											"I was able to select an appointment time that was convenient for me.",
										answer: "Agree",
									},
								],
								feedback: selectedReview.comment,
							}
						: undefined
				}
			/>

			{/* Survey Question Sheet */}
			<SurveyQuestionSheet
				open={surveyQuestionSheetOpen}
				onOpenChange={setSurveyQuestionSheetOpen}
				questionData={
					selectedQuestion &&
					selectedQuestion.type !== "1 - 10 Scale" &&
					selectedQuestion.type !== "Yes / No" &&
					selectedQuestion.type !== "Scale (Disagree to Agree)"
						? {
								question: selectedQuestion.question,
								type: selectedQuestion.type,
								totalResponses: selectedQuestion.responses,
								responses:
									getResponseDataForQuestion(
										selectedQuestion
									),
							}
						: undefined
				}
			/>

			{/* Scale Survey Question Sheet */}
			<ScaleSurveyQuestionSheet
				open={scaleSurveyQuestionSheetOpen}
				onOpenChange={setScaleSurveyQuestionSheetOpen}
				questionData={
					selectedQuestion && selectedQuestion.type === "1 - 10 Scale"
						? {
								question:
									selectedQuestion.fullQuestion ||
									selectedQuestion.question,
								type: selectedQuestion.type,
								totalResponses: selectedQuestion.responses,
								responses: getResponseDataForQuestion(
									selectedQuestion
								) as any,
							}
						: undefined
				}
			/>

			{/* Yes/No Survey Question Sheet */}
			<YesNoSurveyQuestionSheet
				open={yesNoSurveyQuestionSheetOpen}
				onOpenChange={setYesNoSurveyQuestionSheetOpen}
				questionData={
					selectedQuestion && selectedQuestion.type === "Yes / No"
						? {
								question:
									selectedQuestion.fullQuestion ||
									selectedQuestion.question,
								type: selectedQuestion.type,
								totalResponses: selectedQuestion.responses,
								responses: getResponseDataForQuestion(
									selectedQuestion
								) as any,
							}
						: undefined
				}
			/>

			{/* Likert Scale Survey Question Sheet */}
			<LikertScaleSurveyQuestionSheet
				open={likertScaleSurveyQuestionSheetOpen}
				onOpenChange={setLikertScaleSurveyQuestionSheetOpen}
				questionData={
					selectedQuestion &&
					selectedQuestion.type === "Scale (Disagree to Agree)"
						? {
								question:
									selectedQuestion.fullQuestion ||
									selectedQuestion.question,
								type: selectedQuestion.type,
								totalResponses: selectedQuestion.responses,
								responses: getResponseDataForQuestion(
									selectedQuestion
								) as any,
							}
						: undefined
				}
			/>

			{/* Text Response Survey Question Sheet */}
			<TextResponseSurveyQuestionSheet
				open={textResponseSurveyQuestionSheetOpen}
				onOpenChange={setTextResponseSurveyQuestionSheetOpen}
				questionData={
					selectedQuestion &&
					(selectedQuestion.type === "Open Text" ||
						selectedQuestion.type === "Text")
						? {
								question:
									selectedQuestion.fullQuestion ||
									selectedQuestion.question,
								type: selectedQuestion.type,
								totalResponses: selectedQuestion.responses,
								responses: [
									{
										id: "1",
										text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
										userName: "Jhon Doe",
										userEmail: "<EMAIL>",
										date: "03 May 2025",
									},
									{
										id: "2",
										text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
										userName: "Jhon Doe",
										userEmail: "<EMAIL>",
										date: "03 May 2025",
									},
									{
										id: "3",
										text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
										userName: "Jhon Doe",
										userEmail: "<EMAIL>",
										date: "03 May 2025",
									},
								],
							}
						: undefined
				}
			/>
		</div>
	);
}
