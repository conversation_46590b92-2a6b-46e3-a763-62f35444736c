import React, { useState, useRef, useEffect } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { X, PaintBucket, CircleMinus, CirclePlus, Check } from "lucide-react";
import { SketchPicker, type ColorResult } from "react-color";
import { Label } from "@/components/ui/label";
import { type DateRange } from "@/components/common/Datepicker/DatePicker";
import {
	LocationSelectionStep,
	type LocationSelectionData,
} from "./LocationSelectionStep";
import { useUpdateCategory } from "@/hooks/useCategories";
import type {
	UpdateCategoryRequest,
	CategoryDetailData,
} from "@/lib/api/categoriesApi";

interface EditCategorySheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	category?: CategoryDetailData;
	onSubmit?: (data: CategoryFormData & LocationSelectionData) => void;
}

export interface CategoryFormData {
	name: string;
	description: string;
	color: string;
	conditions: CategoryCondition[];
}

export interface CategoryCondition {
	id: string;
	categoryCondition: string;
	conditionCheck?: string;
	field?: string;
	parameter?: string;
	dataSource?: string;
	dataSourceList?: string;
	selectedAnswer?: string;
	selectedDate?: Date;
	selectedDateRange?: DateRange;
	priorityLevel?: string;
	selectedForm?: string;
	selectedQuestion?: string;
	selectedFormAnswer?: string;
	selectedCustomIntake?: string;
	selectedCustomIntakeAnswer?: string;
}

const conditionCheckOptions = [
	{ id: "registration", label: "Registration" },
	{ id: "last_visit", label: "Last Visit" },
	{ id: "information_check", label: "Information Check" },
];

const fieldOptions = {
	information_check: [
		{ id: "date_of_birth", label: "Date of Birth" },
		{ id: "patient_information", label: "Patient Information" },
	],
};

const parameterOptions = {
	date_of_birth: [
		{ id: "before_date", label: "Before the Date" },
		{ id: "after_date", label: "After the Date" },
		{ id: "on_date", label: "On the Date" },
		{ id: "during_range", label: "During the Range" },
		{ id: "outside_range", label: "Outside the Range" },
	],
	patient_information: [
		{ id: "priority", label: "Priority" },
		{ id: "selected_answers", label: "Selected Answers from Forms" },
		{ id: "custom_intakes", label: "Custom Intakes" },
	],
	registration: [
		{ id: "before_date", label: "Before the Date" },
		{ id: "after_date", label: "After the Date" },
		{ id: "on_date", label: "On the Date" },
		{ id: "during_range", label: "During the Range" },
		{ id: "outside_range", label: "Outside the Range" },
	],
	last_visit: [
		{ id: "before_date", label: "Before the Date" },
		{ id: "after_date", label: "After the Date" },
		{ id: "on_date", label: "On the Date" },
		{ id: "during_range", label: "During the Range" },
		{ id: "outside_range", label: "Outside the Range" },
	],
};

const dataSourceOptions = [
	{ id: "dr_amins_genital_concern", label: "Dr.Amin's Genital Concern" },
	{ id: "gender", label: "Gender" },
];

const answerOptions = [
	{ id: "female", label: "Female" },
	{ id: "male", label: "Male" },
];

const priorityLevels = [
	{ id: "high", label: "High" },
	{ id: "medium", label: "Medium" },
	{ id: "normal", label: "Normal" },
];

const formsData = [
	{ id: "medical_history", label: "Medical History Form" },
	{ id: "symptoms_assessment", label: "Symptoms Assessment Form" },
	{ id: "patient_intake", label: "Patient Intake Form" },
	{ id: "follow_up", label: "Follow-up Form" },
];

const questionsData = {
	medical_history: [
		{ id: "allergies", label: "Do you have any allergies?" },
		{
			id: "medications",
			label: "Are you currently taking any medications?",
		},
		{
			id: "previous_surgeries",
			label: "Have you had any previous surgeries?",
		},
	],
	symptoms_assessment: [
		{ id: "pain_level", label: "What is your pain level (1-10)?" },
		{
			id: "symptom_duration",
			label: "How long have you had these symptoms?",
		},
		{
			id: "symptom_frequency",
			label: "How often do you experience these symptoms?",
		},
	],
	patient_intake: [
		{ id: "chief_complaint", label: "What is your main concern today?" },
		{ id: "insurance_type", label: "What type of insurance do you have?" },
		{ id: "emergency_contact", label: "Who is your emergency contact?" },
	],
	follow_up: [
		{ id: "improvement", label: "Have you noticed any improvement?" },
		{ id: "side_effects", label: "Are you experiencing any side effects?" },
		{
			id: "additional_concerns",
			label: "Do you have any additional concerns?",
		},
	],
};

const formAnswersData = {
	allergies: [
		{ id: "no_allergies", label: "No known allergies" },
		{ id: "drug_allergies", label: "Drug allergies" },
		{ id: "food_allergies", label: "Food allergies" },
		{ id: "environmental_allergies", label: "Environmental allergies" },
	],
	medications: [
		{ id: "no_medications", label: "No current medications" },
		{ id: "prescription_only", label: "Prescription medications only" },
		{ id: "otc_only", label: "Over-the-counter medications only" },
		{ id: "both_types", label: "Both prescription and OTC medications" },
	],
	pain_level: [
		{ id: "mild_1_3", label: "Mild (1-3)" },
		{ id: "moderate_4_6", label: "Moderate (4-6)" },
		{ id: "severe_7_10", label: "Severe (7-10)" },
	],
	chief_complaint: [
		{ id: "routine_checkup", label: "Routine checkup" },
		{ id: "specific_symptoms", label: "Specific symptoms" },
		{ id: "follow_up_visit", label: "Follow-up visit" },
		{ id: "medication_refill", label: "Medication refill" },
	],
};

const customIntakesData = [
	{ id: "dietary_preferences", label: "Dietary Preferences Intake" },
	{ id: "lifestyle_assessment", label: "Lifestyle Assessment Intake" },
	{ id: "mental_health_screening", label: "Mental Health Screening Intake" },
	{ id: "family_history", label: "Family History Intake" },
	{ id: "exercise_habits", label: "Exercise Habits Intake" },
];

const customIntakeAnswersData = {
	dietary_preferences: [
		{ id: "vegetarian", label: "Vegetarian" },
		{ id: "vegan", label: "Vegan" },
		{ id: "keto", label: "Ketogenic" },
		{ id: "mediterranean", label: "Mediterranean" },
		{ id: "no_restrictions", label: "No dietary restrictions" },
	],
	lifestyle_assessment: [
		{ id: "active", label: "Very Active" },
		{ id: "moderate", label: "Moderately Active" },
		{ id: "sedentary", label: "Sedentary" },
		{ id: "irregular", label: "Irregular Activity" },
	],
	mental_health_screening: [
		{ id: "excellent", label: "Excellent" },
		{ id: "good", label: "Good" },
		{ id: "fair", label: "Fair" },
		{ id: "poor", label: "Poor" },
		{ id: "prefer_not_to_say", label: "Prefer not to say" },
	],
	family_history: [
		{ id: "diabetes", label: "Diabetes" },
		{ id: "heart_disease", label: "Heart Disease" },
		{ id: "cancer", label: "Cancer" },
		{ id: "hypertension", label: "Hypertension" },
		{ id: "no_family_history", label: "No significant family history" },
	],
	exercise_habits: [
		{ id: "daily", label: "Daily exercise" },
		{ id: "weekly", label: "3-5 times per week" },
		{ id: "occasional", label: "1-2 times per week" },
		{ id: "rarely", label: "Rarely exercise" },
		{ id: "never", label: "Never exercise" },
	],
};

const colorPalette = [
	"#222A31",
	"#5E98C9",
	"#0277D8",
	"#9A76C9",
	"#32BA3F",
	"#E36F6F",
	"#E9ED18",
	"#FF9500",
	"#C77676",
	"#FA38C4",
	"#54758F",
	"#A3762D",
];

export function EditCategorySheet({
	open,
	onOpenChange,
	category,
	onSubmit,
}: EditCategorySheetProps) {
	const [currentStep, setCurrentStep] = useState<
		"category" | "locations" | "success"
	>("category");
	const [formData, setFormData] = useState<CategoryFormData>({
		name: "",
		description: "",
		color: "#000000",
		conditions: [
			{
				id: "1",
				categoryCondition: "",
			},
		],
	});
	const [locationData, setLocationData] = useState<LocationSelectionData>({
		applyToAll: false,
		selectedLocations: [],
		selectedStations: {},
		locationSelections: [],
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [showColorPicker, setShowColorPicker] = useState(false);
	const colorPickerRef = useRef<HTMLDivElement>(null);

	const updateCategoryMutation = useUpdateCategory({
		onSuccess: () => {
			setCurrentStep("success");
		},
		onError: (error) => {
			console.error("Error updating category:", error);
		},
	});

	useEffect(() => {
		if (category && open) {
			setFormData({
				name: category.name || "",
				description: category.description || "",
				color: category.color || "#000000",
				conditions: [
					{
						id: "1",
						categoryCondition:
							category.type === "manual" ? "None" : "Custom",
					},
				],
			});
			setLocationData({
				applyToAll: false,
				selectedLocations: [],
				selectedStations: {},
				locationSelections: [],
			});
		}
	}, [category, open]);

	const resetForm = () => {
		setFormData({
			name: "",
			description: "",
			color: "#000000",
			conditions: [
				{
					id: "1",
					categoryCondition: "",
				},
			],
		});
		setLocationData({
			applyToAll: true,
			selectedLocations: [],
			selectedStations: {},
			locationSelections: [],
		});
		setCurrentStep("category");
	};

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
	) => {
		const { name, value } = e.target;
		setFormData({
			...formData,
			[name]: value,
		});
	};

	const handleColorSelect = (color: string) => {
		setFormData({
			...formData,
			color,
		});
		setShowColorPicker(false);
	};

	const handleColorPickerChange = (color: ColorResult) => {
		setFormData({
			...formData,
			color: color.hex,
		});
	};

	const toggleColorPicker = () => {
		setShowColorPicker(!showColorPicker);
	};

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				colorPickerRef.current &&
				!colorPickerRef.current.contains(event.target as Node)
			) {
				setShowColorPicker(false);
			}
		};

		if (showColorPicker) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showColorPicker]);

	const handleConditionChange = (
		conditionId: string,
		field: string,
		value: string | string[] | Date | DateRange | undefined
	) => {
		const stringValue = Array.isArray(value)
			? value[0]
			: typeof value === "string"
				? value
				: value;

		setFormData({
			...formData,
			conditions: formData.conditions.map((condition) => {
				if (condition.id === conditionId) {
					const updatedCondition = {
						...condition,
						[field]:
							field === "selectedDate" ||
							field === "selectedDateRange"
								? value
								: stringValue,
					};

					if (field === "categoryCondition") {
						updatedCondition.conditionCheck = "";
						updatedCondition.field = "";
						updatedCondition.parameter = "";
						updatedCondition.dataSource = "";
						updatedCondition.dataSourceList = "";
						updatedCondition.selectedAnswer = "";
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
					} else if (field === "conditionCheck") {
						updatedCondition.field = "";
						updatedCondition.parameter = "";
						updatedCondition.dataSource = "";
						updatedCondition.dataSourceList = "";
						updatedCondition.selectedAnswer = "";
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
					} else if (field === "field") {
						updatedCondition.parameter = "";
						updatedCondition.dataSource = "";
						updatedCondition.dataSourceList = "";
						updatedCondition.selectedAnswer = "";
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
					} else if (field === "parameter") {
						updatedCondition.dataSource = "";
						updatedCondition.dataSourceList = "";
						updatedCondition.selectedAnswer = "";
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
						updatedCondition.priorityLevel = "";
						updatedCondition.selectedForm = "";
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
						updatedCondition.selectedCustomIntake = "";
						updatedCondition.selectedCustomIntakeAnswer = "";
					} else if (field === "dataSource") {
						updatedCondition.dataSourceList = "";
						updatedCondition.selectedAnswer = "";
					} else if (field === "dataSourceList") {
						updatedCondition.selectedAnswer = "";
					} else if (field === "selectedForm") {
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
					} else if (field === "selectedQuestion") {
						updatedCondition.selectedFormAnswer = "";
					} else if (field === "selectedCustomIntake") {
						updatedCondition.selectedCustomIntakeAnswer = "";
					}

					return updatedCondition;
				}
				return condition;
			}),
		});
	};

	const handleAddCondition = () => {
		if (formData.conditions.length < 5) {
			const newCondition: CategoryCondition = {
				id: Date.now().toString(),
				categoryCondition: "", // Start with empty value
			};
			setFormData({
				...formData,
				conditions: [...formData.conditions, newCondition],
			});
		}
	};

	const handleRemoveCondition = (conditionId: string) => {
		if (formData.conditions.length > 1) {
			setFormData({
				...formData,
				conditions: formData.conditions.filter(
					(condition) => condition.id !== conditionId
				),
			});
		}
	};

	// Transform form data to API format
	const transformToApiFormat = (
		formData: CategoryFormData,
		locationData: LocationSelectionData
	): UpdateCategoryRequest => {
		// Check if category condition is "None" (manual type)
		const isManualCategory = formData.conditions.every(
			(condition) =>
				condition.categoryCondition === "None" ||
				condition.categoryCondition === ""
		);

		console.log("Form conditions:", formData.conditions);
		console.log("Is manual category (None condition):", isManualCategory);
		console.log("Location data applyToAll:", locationData.applyToAll);

		const apiData: UpdateCategoryRequest = {
			id: category?.id || 0,
			name: formData.name,
			description: formData.description,
			color: formData.color,
			type: isManualCategory ? "manual" : "conditional",
		};

		if (isManualCategory) {
			if (locationData.applyToAll) {
				apiData.apply_to_all_locations = true;
			} else {
				apiData.location_selections = locationData.locationSelections;
				const allLocationIds: number[] = [];
				const allStationIds: number[] = [];

				locationData.locationSelections.forEach((selection) => {
					allLocationIds.push(selection.location_id);
					if (!selection.all_stations) {
						allStationIds.push(...selection.station_ids);
					}
				});

				apiData.location_ids = allLocationIds;
				apiData.station_ids = allStationIds;
			}
			apiData.client_ids = [];
		}

		// TODO: Handle conditional categories when implementing custom conditions
		// if (!isManualCategory) {
		//   apiData.conditions = transformConditions(formData.conditions);
		// }
		return apiData;
	};

	const handleCategorySubmit = () => {
		if (!formData.name.trim()) {
			return;
		}
		setCurrentStep("locations");
	};

	const handleLocationSubmit = async () => {
		setIsSubmitting(true);
		try {
			const apiData = transformToApiFormat(formData, locationData);
			await updateCategoryMutation.mutateAsync(apiData);
		} catch (error) {
			console.error("Error updating category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleSkipLocationSelection = async () => {
		setIsSubmitting(true);
		try {
			const skippedLocationData: LocationSelectionData = {
				applyToAll: true,
				selectedLocations: [],
				selectedStations: {},
				locationSelections: [],
			};
			const apiData = transformToApiFormat(formData, skippedLocationData);
			await updateCategoryMutation.mutateAsync(apiData);
		} catch (error) {
			console.error("Error updating category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	const handleSheetOpenChange = (open: boolean) => {
		if (!open) {
			resetForm();
		}
		onOpenChange(open);
	};

	return (
		<Sheet open={open} onOpenChange={handleSheetOpenChange}>
			<SheetContent className="w-full !max-w-[525px] p-0 [&>button]:hidden">
				{currentStep === "success" ? (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Edit Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="flex flex-1 items-center justify-center">
							{renderSuccessContent()}
						</div>
					</div>
				) : (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Edit Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="min-h-0 flex-1 overflow-y-auto p-6 pt-6">
							{currentStep === "category" &&
								renderCategoryContent()}
							{currentStep === "locations" &&
								renderLocationContent()}
						</div>

						<div className="flex-shrink-0 bg-white p-6">
							{currentStep === "category"
								? renderCategoryFooter()
								: renderLocationFooter()}
						</div>
					</div>
				)}
			</SheetContent>
		</Sheet>
	);

	function renderCategoryContent() {
		return (
			<div className="flex flex-col gap-6">
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Category Details</h3>

					<div className="space-y-2">
						<Label htmlFor="name" className="text-xs font-medium">
							Category Name *
						</Label>
						<Input
							id="name"
							name="name"
							value={formData.name}
							onChange={handleInputChange}
							className="h-9 text-xs"
							placeholder="Enter category name"
						/>
					</div>

					<div className="space-y-2">
						<Label
							htmlFor="description"
							className="text-xs font-medium"
						>
							Description
						</Label>
						<Textarea
							id="description"
							name="description"
							value={formData.description}
							onChange={handleInputChange}
							className="min-h-20 resize-none text-xs"
							placeholder="Category Description"
						/>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Assign Color</h3>

					<div className="relative flex items-center gap-2">
						<button
							type="button"
							onClick={toggleColorPicker}
							className="flex h-9 items-center gap-4 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200"
						>
							<PaintBucket className="h-5 w-5" />
							<div
								className="h-6 w-6 rounded-md"
								style={{
									backgroundColor: formData.color,
								}}
							/>
						</button>
						<Input
							value={formData.color}
							onChange={(e) => handleColorSelect(e.target.value)}
							className="h-9 w-24 text-xs"
							placeholder="#000000"
						/>

						{showColorPicker && (
							<div
								ref={colorPickerRef}
								className="absolute bottom-full left-0 z-50 mb-2 border border-gray-200 bg-white shadow-lg"
								style={{ borderRadius: "8px" }}
							>
								<style>{`
									.sketch-picker-custom .flexbox-fix:last-child {
										display: none !important;
									}
									.sketch-picker-custom .flexbox-fix:nth-last-child(2) {
										display: none !important;
									}
								`}</style>
								<div className="sketch-picker-custom">
									<SketchPicker
										color={formData.color}
										onChange={handleColorPickerChange}
										disableAlpha={true}
										presetColors={[]}
									/>
								</div>
							</div>
						)}
					</div>

					<div className="flex flex-col gap-1.5">
						<p className="text-[10px] text-gray-500">Recent</p>
						<div className="flex flex-wrap gap-1.5">
							{colorPalette.map((color, index) => (
								<button
									key={index}
									type="button"
									onClick={() => handleColorSelect(color)}
									className={`relative h-7 w-7 rounded-md transition-all ${
										formData.color === color
											? "ring-2 ring-blue-500 ring-offset-1"
											: ""
									}`}
									style={{
										backgroundColor: color,
									}}
								>
									{formData.color === color && (
										<div className="absolute inset-0 flex items-center justify-center">
											<Check className="h-3.5 w-3.5 text-white" />
										</div>
									)}
								</button>
							))}
						</div>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Conditions</h3>

					{formData.conditions.map((condition) => (
						<div key={condition.id} className="flex flex-col gap-4">
							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Category Condition *
								</Label>
								<Select
									value={condition.categoryCondition}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"categoryCondition",
											value
										)
									}
								>
									<SelectTrigger className="mt-2 h-9 w-full text-xs">
										<SelectValue placeholder="Select condition" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="None">
											None
										</SelectItem>
										<SelectItem value="Custom">
											Custom
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{condition.categoryCondition === "Custom" && (
								<div className="space-y-2">
									<Label className="text-xs font-medium">
										Condition Check *
									</Label>
									<Select
										value={condition.conditionCheck || ""}
										onValueChange={(value) =>
											handleConditionChange(
												condition.id,
												"conditionCheck",
												value
											)
										}
									>
										<SelectTrigger className="mt-2 h-9 w-full text-xs">
											<SelectValue placeholder="Select condition check" />
										</SelectTrigger>
										<SelectContent>
											{conditionCheckOptions.map(
												(option) => (
													<SelectItem
														key={option.id}
														value={option.id}
													>
														{option.label}
													</SelectItem>
												)
											)}
										</SelectContent>
									</Select>
								</div>
							)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Field *
										</Label>
										<Select
											value={condition.field || ""}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"field",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select field" />
											</SelectTrigger>
											<SelectContent>
												{fieldOptions.information_check.map(
													(option) => (
														<SelectItem
															key={option.id}
															value={option.id}
														>
															{option.label}
														</SelectItem>
													)
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.field && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Parameter *
										</Label>
										<Select
											value={condition.parameter || ""}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"parameter",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select parameter" />
											</SelectTrigger>
											<SelectContent>
												{parameterOptions[
													condition.field as keyof typeof parameterOptions
												]?.map((option) => (
													<SelectItem
														key={option.id}
														value={option.id}
													>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								(condition.conditionCheck === "registration" ||
									condition.conditionCheck ===
										"last_visit") && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Parameter *
										</Label>
										<Select
											value={condition.parameter || ""}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"parameter",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select parameter" />
											</SelectTrigger>
											<SelectContent>
												{parameterOptions[
													condition.conditionCheck as keyof typeof parameterOptions
												]?.map((option) => (
													<SelectItem
														key={option.id}
														value={option.id}
													>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}

							{formData.conditions.length > 1 && (
								<div className="flex justify-end">
									<button
										type="button"
										onClick={() =>
											handleRemoveCondition(condition.id)
										}
										className="flex items-center gap-2 text-xs text-[#27272A]"
									>
										<CircleMinus className="h-3 w-3" />
										Remove Condition
									</button>
								</div>
							)}
						</div>
					))}

					{formData.conditions.length < 5 &&
						formData.conditions.some(
							(condition) =>
								condition.categoryCondition === "Custom"
						) && (
							<div className="flex items-center justify-between">
								<div className="flex flex-col gap-0.5">
									<button
										type="button"
										onClick={handleAddCondition}
										className="flex items-center gap-2 text-xs text-[#27272A]"
									>
										<CirclePlus className="h-3 w-3" />
										Add Another Condition
									</button>
									<div className="pl-5 text-[8px] text-gray-500">
										(Max 5)
									</div>
								</div>
							</div>
						)}
				</div>
			</div>
		);
	}

	function renderCategoryFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 px-4 text-xs"
				>
					Cancel
				</Button>
				<Button
					onClick={handleCategorySubmit}
					disabled={isSubmitting || !formData.name.trim()}
					className="h-9 bg-[#005893] px-4 text-xs hover:bg-[#004a7a]"
				>
					Next
				</Button>
			</div>
		);
	}

	function renderLocationContent() {
		return (
			<LocationSelectionStep
				locationData={locationData}
				onLocationDataChange={setLocationData}
			/>
		);
	}

	function renderLocationFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 rounded-md border border-gray-200 bg-white px-4 py-2 text-xs font-medium"
				>
					Cancel
				</Button>
				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						variant="secondary"
						onClick={handleSkipLocationSelection}
						className="h-9 rounded-md bg-gray-100 px-4 py-2 text-xs font-medium"
					>
						Skip for now
					</Button>
					<Button
						onClick={handleLocationSubmit}
						disabled={
							isSubmitting ||
							(!locationData.applyToAll &&
								locationData.selectedLocations.length === 0)
						}
						className="h-9 rounded-md px-4 py-2 text-xs font-medium text-white"
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				</div>
			</div>
		);
	}

	function renderSuccessContent() {
		const handleDone = () => {
			try {
				const finalData = {
					...formData,
					...locationData,
				};
				onSubmit?.(finalData);
				resetForm();
				onOpenChange(false);
			} catch (error) {
				console.error("Error updating category:", error);
			}
		};

		return (
			<div className="flex flex-col items-center justify-center gap-8">
				<div className="flex flex-col items-center justify-start gap-11">
					<div className="inline-flex items-center justify-start gap-2.5 overflow-hidden rounded-[500px] bg-zinc-500/5 p-8">
						<div className="relative h-12 w-12 overflow-hidden">
							<Check
								className="absolute top-3 left-2 h-8 w-8 text-[#005893]/16"
								strokeWidth={4}
							/>
						</div>
					</div>

					<div className="flex w-full max-w-72 flex-col items-center justify-start gap-3">
						<div className="text-foreground text-center text-xl leading-loose font-semibold">
							Category Updated
						</div>
						<div className="text-muted-foreground text-center text-sm leading-tight font-normal">
							The category has been updated successfully.
						</div>
					</div>
				</div>

				<div className="inline-flex items-start justify-center gap-3">
					<Button
						onClick={handleDone}
						className="h-9 w-20 bg-sky-800 px-4 py-2 text-xs font-medium text-white hover:bg-sky-900"
					>
						Done
					</Button>
				</div>
			</div>
		);
	}
}
