import { apiClient } from "./clients";

const PATIENT_EXPERIENCE_ENDPOINTS = {
	base: "/api/v1/patient-experience",
	stats: "/api/v1/patient-experience/stats",
} as const;

export interface PatientExperienceFilters {
	organization_id?: number;
	location_ids?: number[];
	category_ids?: number[];
	station_ids?: number[];
	service_ids?: number[];
	page?: number;
	per_page?: number;
}

export interface PatientFeedback {
	id: string;
	patient_name: string;
	patient_avatar: string;
	station_name: string;
	location_name: string;
	service_name: string;
	rating: number;
	date: string;
}

export interface PatientExperienceResponse {
	success: boolean;
	data: {
		feedbacks: PatientFeedback[];
	};
	message: string;
}

export interface PatientExperienceStats {
	total_submissions: number;
	completed_submissions: number;
	completion_rate: number;
	average_rating: number;
	rating_distribution: {
		"1": number;
		"2": number;
		"3": number;
		"4": number;
		"5": number;
	};
	total_reviews: number;
}

export interface PatientExperienceStatsResponse {
	success: boolean;
	data: {
		stats: PatientExperienceStats;
	};
	message: string;
}

export const patientExperienceApi = {
	// Get patient experience feedbacks with filters
	getPatientExperience: async (
		filters: PatientExperienceFilters = {}
	): Promise<PatientExperienceResponse> => {
		const params = new URLSearchParams();
		
		// Add query parameters
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}
		if (filters.location_ids && filters.location_ids.length > 0) {
			filters.location_ids.forEach(id => params.append("location_ids[]", id.toString()));
		}
		if (filters.category_ids && filters.category_ids.length > 0) {
			filters.category_ids.forEach(id => params.append("category_ids[]", id.toString()));
		}
		if (filters.station_ids && filters.station_ids.length > 0) {
			filters.station_ids.forEach(id => params.append("station_ids[]", id.toString()));
		}
		if (filters.service_ids && filters.service_ids.length > 0) {
			filters.service_ids.forEach(id => params.append("service_ids[]", id.toString()));
		}

		const queryString = params.toString();
		const url = queryString
			? `${PATIENT_EXPERIENCE_ENDPOINTS.base}?${queryString}`
			: PATIENT_EXPERIENCE_ENDPOINTS.base;

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},

	// Get patient experience statistics
	getPatientExperienceStats: async (
		organizationId: number
	): Promise<PatientExperienceStatsResponse> => {
		const response = await apiClient.get(PATIENT_EXPERIENCE_ENDPOINTS.stats, {
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},
};
